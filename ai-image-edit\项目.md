# 前端实现

### 1. 背景图生成

* 通过 AI（tongyi / deepseek / 其他图像生成 API）获取  **无文字背景图 URL** 。
* 存储在 `backgroundUrl` 中，作为编辑画布的背景。

### 2. 可编辑 HTML 文字层

* 两种方式：
  * **轻量版** ：用 `contenteditable` div 包裹文本节点，直接编辑文字。
  * **专业版** ：引入 [GrapesJS]() 作为编辑器，支持拖拽、改样式。

推荐先上轻量版，迭代再接 GrapesJS。

### 3. 页面布局

* **左侧：编辑区** （背景图 + 文字层）。
* **右侧：聊天区** （对话窗口，调用 AI 接口生成背景或 HTML）。

### 4. 预览 & 导出

* 使用 `html2canvas` 将背景 + HTML 渲染成图片。
* 可一键下载 PNG。

# 后端实现

**后端调用 AI的实现：**

图片生成使用：minimax的文生图，设计合适的prompt

海报的生成使用：使用deepseek接口，设计合适的prompt

比如背景的生成：“生成一张 1080x1920 竖版的科技感海报背景图，
蓝色渐变，带有几何线条和光效，
画面干净，没有任何文字和水印。”

比如海报的生成：“”

请生成一个适合叠加在海报背景图上的 HTML 片段。
要求：

- 使用 `<div>` + 内联 style 实现绝对定位。
- 包含一个大标题、副标题和一个按钮。
- 标题位置在上方居中，字体白色，加粗，40px。
- 副标题在标题下方，字体 20px，浅灰色。
- 按钮在底部居中，宽度 200px，高度 50px，背景渐变蓝色，圆角。
- 只返回 HTML 片段，不要任何解释。


后端用  **Node.js + Koa2** 作为 AI 中转服务，统一管理调用。

目录结构：

backend/
├── app.js         # Koa2 主入口

├── configs/  #包含prompt编写配置和不同ai接口的aip key的配置
├── routes/
│   ├── ai.js      # AI 路由
├── services/
│   ├── tongyi.js  # 调用通义 API
│   ├── deepseek.js# 调用 deepseek API
└── package.json
